{"name": "student-web", "version": "3.10.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode beta", "build:beta": "NODE_OPTIONS='--max-old-space-size=8192' vite build --mode beta", "build:pre": "NODE_OPTIONS='--max-old-space-size=8192' vite build --mode preprod", "build": "NODE_OPTIONS='--max-old-space-size=8192' vite build --mode production", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@aws-sdk/client-s3": "^3.840.0", "@aws-sdk/lib-storage": "^3.840.0", "@better-scroll/core": "^2.5.1", "@better-scroll/mouse-wheel": "^2.5.1", "@lottiefiles/lottie-player": "^2.0.12", "@neosjs/h5-graffiti": "1.1.4", "@neosjs/h5-like": "^1.0.1", "@thinkacademy/courseware-player": "10.4.5-beta", "@thinkacademy/live-framework": "^1.1.0", "@thinkacademy/new-scroll": "^1.0.1", "@thinkacademy/new-white-board": "^1.8.3", "@thinkacademy/nwb": "^5.0.3", "@thinkacademy/praise-list": "^0.0.24", "@thinkacademy/s3-folder-upload": "^1.1.4", "@thinkacademy/think-web-tboard": "^1.0.1", "@thinkacademy/vitas-utils": "^1.1.3", "@thinkacademy/vue-baberrage": "3.3.11", "@thinkacademy/website-base": "^1.2.7", "@thinkacademy/white-board-tools": "^0.0.6", "@vue/compat": "^3.5.13", "@vueuse/core": "^13.2.0", "address": "^2.0.3", "agora-rtc-sdk-ng": "^4.23.1", "ali-oss": "^6.16.0", "ant-design-vue": "^4.2.6", "aws-sdk": "^2.824.0", "axios": "^1.7.9", "crypto-js": "^4.2.0", "driver.js": "^1.3.6", "hyphenate-style-name": "^1.0.3", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "logan-web-vite": "^1.0.1", "long": "^5.3.0", "mitt": "^3.0.1", "pinia": "^2.3.1", "qs": "^6.14.0", "rollup-plugin-terser": "^5.2.0", "sa-sdk-javascript": "^1.27.2", "sass-embedded": "^1.83.4", "to-px": "^1.1.0", "video.js": "^8.22.0", "vite-plugin-s3": "^1.0.2", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.18.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@rollup/plugin-commonjs": "^28.0.3", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "form-data": "^4.0.3", "less": "^4.2.2", "node-fetch": "^3.3.2", "prettier": "^3.4.2", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.0.11", "vite-plugin-vue-devtools": "^7.7.0", "vite-svg-loader": "^5.1.0"}}