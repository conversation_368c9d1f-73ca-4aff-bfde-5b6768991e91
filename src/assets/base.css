/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;

  --vt-c-indigo: #2c3e50;

  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;
}
:root {
  --color-name: 222, 226, 231;
  --no-teacher-img: url('@/components/VideoGroup/img/teacher-no-camera.png');
  --font-size-12: 12px;
  --font-size-14: 14px;
  --font-size-16: 16px;
  --font-size-18: 18px;
  --font-size-22: 22px;
  --green-mic-img: url('@/components/VideoGroup/img/green_mic.png');
  --off-mic-img: url('@/components/VideoGroup/img/off_mic.png');
  --white-mic-img: url('@/components/VideoGroup/img/white_mic.png');
  --scroll-chat-img: url('@/components/VideoGroup/img/chat-01.png');
  --teacher-onStage-img: url('@/components/VideoGroup/img/onStage.png');
  --on-student-bg-img: url('@/components/VideoGroup/img/black-lines-bg.png');
  --student-onStage-img: url('@/components/VideoGroup/img/bg-mult-video-link.png');
  --level-1-img: url('@/components/VideoGroup/img/level-1.png');
  --level-2-img: url('@/components/VideoGroup/img/level-2.png');
  --level-3-img: url('@/components/VideoGroup/img/level-3.png');
  --level-4-img: url('@/components/VideoGroup/img/level-4.png');
  --level-5-img: url('@/components/VideoGroup/img/level-5.png');
  --level-6-img: url('@/components/VideoGroup/img/level-6.png');
  --level-7-img: url('@/components/VideoGroup/img/level-7.png');
  --count-0-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-0.png');
  --count-1-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-1.png');
  --count-2-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-2.png');
  --count-3-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-3.png');
  --count-4-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-4.png');
  --count-5-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-5.png');
  --count-6-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-6.png');
  --count-7-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-7.png');
  --count-8-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-8.png');
  --count-9-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/count-9.png');
  --count-m-img: url('@/components/VideoGroup/StudentTool/PairIcon/imgs/max.png');

  --level-1-color: 74, 210, 255;
  --level-2-color: 25, 206, 156;
  --level-3-color: 81, 185, 255;
  --level-4-color: 99, 206, 68;
  --level-5-color: 98, 54, 255;
  --level-6-color: 255, 127, 172;
  --level-7-color: 255, 69, 94;
}
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-2);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
  padding: 0;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
