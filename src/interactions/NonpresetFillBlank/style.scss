.fill-blank-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 58.4vw;
  z-index: 999;
  animation: 0.4s ease show forwards;
  opacity: 0;
  .keyboard-input {
    width: 100%;
    height: auto;
    background: url('./imgs/input_bg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .expand-btn {
      width: 7.8%;
      height: auto;
      left: 6.9%;
      position: absolute;
      cursor: pointer;
      &:hover {
        animation: scaleLarge 0.08s linear;
      }
    }
    .input-container {
      width: 40%;
      height: 60px;
      margin: 15px 0 19px 0;
      position: relative;
      .placeholder {
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #643315;
        font-size: 18px;
      }
      .math-input {
        width: 100%;
        height: 100%;
        background: url('./imgs/input_container.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border: none;
        color: #6E3205;
      }
    }
    :deep(.mq-math-mode) {
      .mq-root-block {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .mq-non-leaf {
        .mq-cursor:only-child:after, .mq-empty:after {
          border: none;
        }
        .mq-numerator, .mq-denominator .content {
          border-radius: 2px;
          border: 2px dotted #CCCCCC;
          min-width: 28px;
        }
        .mq-numerator {
          width: fit-content;
          margin: 0 auto;
        }
        .mq-denominator .content {
          display: inline-block;
        }
        .mq-hasCursor {
          border: 2px dotted #8E4814 !important;
        }
        // 分式中间的线
        .mq-line {
          margin-top: 2px;
          border: none;
          background-color: #8E4814;
        }
      }
    }
    .submit-btn {
      width: 16.7%;
      height: auto;
      right: 2.3%;
      position: absolute;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 100%;
        height: 100%;
      }
      span {
        position: absolute;
        color: #fff;
        font-size: 16px;
      }
    }
    :deep(.mq-math-mode *) {
      font-size: 18px;
      font-family: SFProRounded-Medium, SFProRounded;
    }
  }
  .keyboard-container {
    position: relative;
    width: 100%;
    padding: 11px 0;
    background: url('./imgs/keyboard_bg.png');
    .keyboard-panel {
      width: 96%;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: flex-end;
      flex-wrap: wrap;
      .keyboard-item {
        width: 9%;
        height: auto;
        position: relative;
        cursor: pointer;
        img {
          width: 100%;
          height: 100%;
        }
        .text {
          position: absolute;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #FFFFFF;
          font-size: 16px;
        }
      }
    }
  }
  .hover_style {
    animation: scaleLarge 0.08s linear;
  }
  .click-style {
    animation: scaleSmall 0.2s linear;
  }
}
@keyframes scaleLarge {
  to {
    transform: scale(1.1);
  }
}
@keyframes scaleSmall {
  to {
    transform: scale(0.9);
  }
}
@keyframes show {
  0% {
    transform: translateY(300px);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}
// 有正确答案的投票结果样式
.vote-result-isCorrect {
  width: 64.8%;
  height: 100%;
  position: absolute;
  z-index: 67; // 高于涂鸦层
  transform: translateX(-50%);
  top: 0;
}
.myAnswer-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  height: auto;
  background: url('./imgs/myAnswer_bg.png') no-repeat;
  background-size: 100% 100%;
  z-index: 999;
  padding: 21px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .note-container {
    width: 100%;
    display: flex;
    align-items: center;
    .note-img {
      width: 5%;
      height: auto;
      margin-left: 3%;
      margin-right: 2px;
    }
    .answer-desc {
      font-weight: 600;
      color: #6E3205;
      font-size: 18px;
    }
  }
  .lottie-img {
    width: 8.25%;
    margin-right: 4.5%;
    height: auto;
  }
  .answer-text {
    position: absolute;
    top:50%;
    left:50%;
    transform:translate(-50%,-50%);
    font-size: 22px;
  }
  .mq-math-mode {
    border: none;
  }
  :deep(.mq-math-mode *) {
    font-family: SFProRounded-Medium, SFProRounded;
  }
}
.toast-tips {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 999;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  width: 300px;
  height: 80px;
  font-size: 20px;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
}
