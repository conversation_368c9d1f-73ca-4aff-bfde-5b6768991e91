<template>
  <div class="vote-options">
    <div
      v-for="(option, index) in options"
      :key="index"
      class="option-item"
      :data-log="`选项-课件内单选选项-${option}`"
      :class="{ active: selectedOption === option }"
      @click="selectOption(option)"
    >
      <span>{{ option }}</span>
    </div>
  </div>
</template>

<script>
import logger from '@/utils/logger'
export default {
  name: 'SingleOption',
  props: {
    options: {
      type: Array,
      required: true
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedOption: this.value
    }
  },
  watch: {
    value(newValue) {
      this.selectedOption = newValue
    }
  },
  methods: {
    sendLogger(msg, level = 'info') {
      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: 'Vote'
        }
      })
    },
    selectOption(option) {
      this.sendLogger(`点击-课件内选择题选项-${option}`)
      this.selectedOption = option
      this.$emit('input', option)
      this.$emit('change', option)
    }
  }
}
</script>

<style lang="scss" scoped>
.vote-options {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;

  .option-item {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border-radius: 12px;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: 2px solid transparent;

    span {
      white-space: nowrap;
      color: #333;
      font-weight: bold;
    }

    &:hover {
      background: rgba(255, 255, 255, 1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.active {
      background: #fff !important;
      color: #FF6B00 !important;
      border-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      span {
        color: #FF6B00 !important;
        font-weight: bold !important;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .vote-options {
    gap: 10px;

    .option-item {
      width: 55px;
      height: 55px;
      font-size: 16px;
    }
  }
}
</style>
