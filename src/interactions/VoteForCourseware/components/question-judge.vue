<template>
  <div class="answer-box">
    <div
      class="my-answer-box option-item option-item-courseware"
      v-if="theUserAnswer && theUserAnswer !== theQuesAnswer"
    >
      <div class="option-item-res-desc">
        <div class="option-name">
          <!--quesTypeId=5判断题端上单独处理-->
          <template v-if="quesTypeId === 5">
           {{
              theUserAnswer === 'T'
                ? $t('classroom.interactions.coursewareBoard.true')
                : $t('classroom.interactions.coursewareBoard.false')
            }}
          </template>
          <template v-else>
            <template v-for="item in userAnswer">
              {{ item[0] }}
            </template>
          </template>
        </div>
        <div class="option-text">
          {{ $t('classroom.interactions.coursewareBoard.yourAnswer') }}
        </div>
      </div>
    </div>
    <div class="right-answer-box option-item option-item-courseware">
      <div class="option-item-res-desc">
        <div class="option-name correct-answer-option">
          <template v-if="quesTypeId === 5">
           {{
              theQuesAnswer === 'T'
                ? $t('classroom.interactions.coursewareBoard.true')
                : $t('classroom.interactions.coursewareBoard.false')
            }}
          </template>
          <template v-else>
            <template v-for="item in quesAnswer">{{ item[0] }}</template>
          </template>
        </div>
        <div class="option-text correct-answer-text">
          {{ $t('classroom.interactions.coursewareBoard.correctAnswer') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuestionJudge',
  props: {
    quesTypeId: {
      type: Number,
      default: 0
    },
    userAnswer: {
      type: Array,
      default: () => {
        return []
      }
    },
    quesAnswer: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    theUserAnswer() {
      console.log('quesTypeId', this.quesTypeId)
      return [...new Set(this.userAnswer)].sort().toString()
    },
    theQuesAnswer() {
      return [...new Set(this.quesAnswer)].sort().toString()
    }
  }
}
</script>
