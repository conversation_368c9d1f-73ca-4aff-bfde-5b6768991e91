<template>
  <div
    :class="
      isSubmit && !isExceptionStatus
        ? 'courseware-board-container-musk'
        : 'courseware-board-container'
    "
    v-show="visible"
  >
    <audio :src="audioFiles.interactFail" class="hide" ref="interactFail"></audio>
    <audio :src="audioFiles.interactKeyTone" class="hide" ref="interactKeyTone"></audio>
    <audio :src="audioFiles.interactSubmit" class="hide" ref="interactSubmit"></audio>
    <audio :src="audioFiles.interactSuccess" class="hide" ref="interactSuccess"></audio>
    <audio :src="audioFiles.getCoins" ref="coinAudio"></audio>
    <div
      class="vote-bar-container"
      ref="coursewareBoardBlock"
      :class="isExpanded ? 'ease-out' : 'ease-in'"
    >
      <!-- 答题 -->
      <template v-if="!isSubmit">
        <div class="vote-pri-bar-container" @click="isExpanded = !isExpanded">
          <img :src="easeInIconImg" v-if="isExpanded" class="ease-icon" />
          <img :src="easeOutIconImg" v-else class="ease-icon" />
        </div>
        <div class="vote-wrapper" data-log="选项box">
          <div
            class="timer-box"
            ref="timerBox"
            :class="[isSubmit ? 'hidden' : '']"
            v-if="countdownOptions"
          >
            <!-- 倒计时替换 -->
            <NeCountdown
              v-if="countdownOptions"
              :time="countdownOptions"
              @complete="countdownComplete"
            ></NeCountdown>
          </div>
          <QuestionOption
            :optionList="optionList"
            :quesTypeId="quesTypeId"
            @change="upodateUserAnswer"
          />
          <div class="vote-submit-container">
            <div
              class="vote-submit"
              :class="!btnDisabled ? 'isSelected' : ''"
              @click.stop="submitAnswer(true)"
            >
              {{ $t('common.submit') }}
            </div>
          </div>
        </div>
      </template>

      <!-- 判题 -->
      <template v-if="isSubmit && !isExceptionStatus">
        <div class="vote-wrapper-result">
          <div class="vote-title res vote-title"></div>

          <QuestionJudge
            :quesTypeId="quesTypeId"
            :userAnswer="userAnswer"
            :quesAnswer="quesAnswer"
          />
          <div class="vote-res-close-container">
            <div class="icon-close" @click="destroy"></div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import coursewareBoard from './coursewareBoard'
import QuestionOption from './components/question-option.vue'
import QuestionJudge from './components/question-judge.vue'
import NeCountdown from '@/components/NeCountdown/index.vue'
import logger from '@/utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { emitter } from '@/hooks/useEventBus'

// 导入音频文件
import interactFailAudio from './audio/interact-fail.mp3'
import interactKeyToneAudio from './audio/interact-key-tone.mp3'
import interactSubmitAudio from './audio/interact-submit.mp3'
import interactSuccessAudio from './audio/interact-success.mp3'
import getCoinsAudio from './audio/get-coins.mp3'

// 导入图片资源
import easeInIconImg from './img/ease-in-icon.png'
import easeOutIconImg from './img/ease-out-icon.png'

export default {
  name: 'VoteForCourseware',
  props: {
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      quesAnswer: [], // 正确答案
      optionList: [], // 选项列表
      quesTypeId: null, // 题目类型ID 1:单选 2:多选 5:判断
      clickDisabled: false, // 禁止重复点击
      btnDisabled: true, // 提交按钮状态
      userAnswer: [], // 用户选择的答案
      isExpanded: false, // 是否关闭答题板
      isSubmit: false, // 是否已答题
      isExceptionStatus: false, // 是否异常恢复状态
      countdownOptions: null, // 倒计时参数
      visible: false, // 是否展示题板
      teacherClosedInteract: false, // 老师主动关闭互动
      // 图片资源
      easeInIconImg,
      easeOutIconImg,
      audioFiles: {
        interactFail: interactFailAudio,
        interactKeyTone: interactKeyToneAudio,
        interactSubmit: interactSubmitAudio,
        interactSuccess: interactSuccessAudio,
        getCoins: getCoinsAudio
      }
    }
  },
  components: { QuestionOption, QuestionJudge, NeCountdown },
  async created() {
    this.initEventListener()
    this.coursewareBoard = new coursewareBoard({
      ...this.options
    })
    this.$nextTick(() => {
      // 获取试卷详情
      const quesInfo = this.coursewareBoard.quesInfo
      this.optionList = quesInfo.quesOptions
      this.quesAnswer = quesInfo.quesAnswer
      this.quesTypeId = quesInfo.quesTypeId
      this.sendLogger('收到互动', 'start')
      this.coursewareBoard.interactReport() // 开启上报
      const res = this.questionStatus() // 异常恢复
      this.sendLogger(`上报结果${JSON.stringify(res)}`, 'report')
      this.countdownOptions = this.coursewareBoard.initCountdown()
      this.sendLogger(`互动倒计时${this.countdownOptions}`, 'countdown')
    })
  },
  computed: {
    iconClass() {
      return this.isExpanded
        ? 'ne-icon-courseware-arrow ne-icon-courseware-up'
        : 'ne-icon-courseware-arrow'
    }
  },
  methods: {
    /**
     * 注册监听事件
     */
    initEventListener() {
      // 先初始化连对弹框为关闭状态
      emitter.emit('initClose')
      emitter.on('closeContinusCorrect', data => {
        // 如果老师关闭互动，则作答结果面板随金币奖励弹窗一起消失，否则等点击关闭按钮才关闭
        if (data && this.teacherClosedInteract) {
          this.closeBoard()
        }
      })
    },
    /**
     * 更新用户选择的答案
     */
    upodateUserAnswer(val) {
      const answerText = Array.isArray(val) ? val.join(',') : val
      this.sendLogger(`学生选择答案 - ${answerText}`, 'answer')
      this.$refs.interactKeyTone.play()
      this.userAnswer = val
    },

    /**
     * 关闭面板
     */
    closeBoard() {
      this.isExpanded = true
      this.$refs.coursewareBoardBlock &&
        this.$refs.coursewareBoardBlock.addEventListener(
          'transitionend',
          () => {
            this.destroy()
          },
          false
        )
    },

    /**
     * 异常恢复
     */
    async questionStatus() {
      // 异常恢复
      const { code, data } = await this.coursewareBoard.questionStatus().catch(err => {
        this.sendLogger('接口报错:异常恢复', 'error', 'error')
        return err || {}
      })
      if (code === 0) {
        // 已经作答则不展示答题板
        if (data.userAnswer) {
          this.isSubmit = true
          this.isExceptionStatus = true
          this.userAnswer = data.userAnswer
        } else {
          this.visible = true
        }
      }
    },

    /**
     * 提交答案
     */
    async submitAnswer(isClickBtn) {
      if (this.clickDisabled || (isClickBtn && this.userAnswer.length === 0)) {
        return
      }
      this.btnDisabled = true
      this.clickDisabled = true
      this.$refs.interactSubmit && this.$refs.interactSubmit.play()
      const { isSubmit, isRight, params, response } = await this.coursewareBoard.submitAnswer(
        this.userAnswer
      )

      // 获取答题结果描述
      const resultText = isRight === 1 ? '正确' : isRight === 0 ? '错误' : '未作答'
      const answerText = Array.isArray(this.userAnswer)
        ? this.userAnswer.join(',')
        : this.userAnswer || '无答案'
      const submitType = isClickBtn ? '手动提交' : '自动提交'

      console.log('提交答案', isSubmit, isRight, params, response)
      this.isSubmit = isSubmit
      this.clickDisabled = false
      this.sendLogger(`${submitType}答案 - 答案:${answerText} 结果:${resultText}`, 'submit')
      // 自动提交，直接销毁答题板
      await this.$nextTick()
      isRight === 1 ? this.$refs.interactSuccess?.play() : this.$refs.interactFail?.play()
      // 在 this.coursewareBoard.submitAnswer 方法中，触发了 Vue.prototype.$bus.$emit('continuousCorrect', continuousCorrectData),
      classLiveSensor.osta_ia_base_interaction_submit(
        this.options?.ircMsg,
        this.quesTypeId,
        isRight
      )
    },

    /**
     * 倒计时结束
     * 用户没作答，需要主动提交一条答题记录到服务端，并展示正确答案
     */
    countdownComplete() {
      this.sendLogger('倒计时结束自动提交答案结果', 'timeout')
      !this.isSubmit && this.submitAnswer()
    },
    // 教师端主动关闭互动
    destroyInteraction() {
      this.sendLogger('主讲关闭自动提交答案结果', 'destroy')
      this.teacherClosedInteract = true
      // 教师端主动关闭互动 重新提交一次 改为未提交的情况下 自动提交一次
      if (!this.isSubmit) {
        this.submitAnswer()
      } else {
        this.destroy()
      }
    },

    /**
     * 获取题目类型名称
     */
    getQuesTypeName(quesTypeId) {
      const typeMap = {
        1: '单选题',
        2: '多选题',
        4: '填空题',
        5: '判断题'
      }
      return typeMap[quesTypeId] || '未知题型'
    },

    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const quesTypeName = this.getQuesTypeName(this.quesTypeId)
      const interactType = this.quesTypeId
        ? `VoteForCourseware-${quesTypeName}`
        : 'VoteForCourseware'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactType,
          interactId: this.options.ircMsg.interactId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })
      console.log(`[${interactType}][${this.options.ircMsg.interactId}] ${msg}`, { stage, level })
    },

    /**
     * 销毁组件
     */
    destroy() {
      this.sendLogger('结束互动', 'end')
      this.coursewareBoard.closeToast()
      this.$emit('close', 'interact')
      emitter.off('closeContinusCorrect') // 使用emitter替换$bus
    }
  },
  watch: {
    // 监听用户是否选择了答案，选择了则高亮提交按钮，否则，禁用提交按钮
    userAnswer(val) {
      if (val.length > 0) {
        this.btnDisabled = false
      } else {
        this.btnDisabled = true
      }
    }
  }
}
</script>
<style lang="scss">
@use './style.scss' as *;
</style>
