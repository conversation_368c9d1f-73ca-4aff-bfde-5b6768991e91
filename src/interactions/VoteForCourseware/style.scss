// 容器样式
.courseware-board-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  pointer-events: none;
}

.courseware-board-container-musk {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: rgba(0, 0, 0, 0.3);
  pointer-events: auto;
}

// 伸缩图标样式
.ease-icon {
  width: 12px;
  height: 8px;
}

// 隐藏样式
.hidden {
  display: none;
}

// 投票选项样式 - 重新设计为橙色背景紧凑布局
.vote-bar-container {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 600px;
  height: auto;
  background: linear-gradient(135deg, #FF8A00 0%, #FF6B00 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(255, 138, 0, 0.3);
  transition: transform 0.3s ease-in;
  pointer-events: auto;
  padding: 16px 20px;

  & .hide {
    display: none;
  }

  // 伸缩按钮区域
  & .vote-pri-bar-container {
    position: absolute;
    left: 50%;
    top: -30px;
    width: 90px;
    height: 30px;
    background: rgba(0, 0, 0, 0.6);
    transform: translateX(-50%);
    transition: transform 1s;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
  }

  &.ease-in {
    transform: translateX(-50%) translateY(0);
  }
  &.ease-out {
    transform: translateX(-50%) translateY(100%);
  }

  & .vote-wrapper, & .vote-wrapper-result {
    display: flex;
    align-items: center;
    background: transparent;
    border-radius: 0;
    position: relative;
    gap: 24px;
  }

  // 投票标题样式 - 修复文字颜色
  & .vote-title {
    color: #ffffff !important;
    width: 110px;
    font-weight: bold;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin-left: 12px;
    
    p {
      color: #ffffff !important;
      margin: 0;
      font-size: 14px;
      font-weight: bold;
    }
    
    // 确保所有子元素文字为白色
    * {
      color: #ffffff !important;
    }
  }

  // 倒计时区域 - 桌面端专用优化
  & .timer-box {
    color: #fff !important;
    width: 110px;
    font-weight: bold;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin-left: 12px;
    
    // 确保所有文字为白色
    * {
      color: #ffffff !important;
    }
    
    // 重写倒计时组件在投票面板中的样式
    .countDownBox {
      position: static !important;
      background: rgba(255, 255, 255, 0.15) !important;
      border: 1px solid rgba(255, 255, 255, 0.3) !important;
      border-radius: 8px !important;
      backdrop-filter: blur(8px) !important;
      padding: 6px 8px !important;
      cursor: default !important;
      z-index: auto !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
      
      // 强制所有子元素文字为白色
      * {
        color: #ffffff !important;
      }
      
      .countDownIcon {
        width: 18px !important;
        height: 22px !important;
        margin-right: 4px !important;
        filter: brightness(0) invert(1) drop-shadow(0 1px 2px rgba(0,0,0,0.2)) !important;
      }
      
      .numBox {
        width: 22px !important;
        height: 22px !important;
        background: rgba(255, 255, 255, 0.25) !important;
        border: 1px solid rgba(255, 255, 255, 0.4) !important;
        border-radius: 4px !important;
        font-size: 14px !important;
        font-weight: 900 !important;
        color: #ffffff !important;
        line-height: 20px !important;
        margin-right: 2px !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.2) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
      }
      
      .colon {
        font-size: 14px !important;
        font-weight: 900 !important;
        color: #ffffff !important;
        margin-right: 2px !important;
        margin-left: 2px !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
      }
    }
    
    .countdown {
      color: #FFE4B5 !important;
      font-size: 18px;
      font-weight: 900;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }

  // 选项区域 - 增加左右边距
  & .vote-options, & .vote-options-res, & .answer-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    gap: 12px;
    margin: 0 16px;
    
    .option-item, .option-item-res {
      width: 60px;
      height: 60px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      border-radius: 12px;
      text-align: center;
      font-weight: bold;
      font-size: 18px;
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      border: 2px solid transparent;
      
      span {
        white-space: nowrap;
      }
      
      &:hover {
        background: rgba(255, 255, 255, 1);
        transform: translateY(-2px);
      }
      
      &.active {
        background: #fff;
        color: #FF6B00;
        border-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
    }
    
    // 判断题选项（TRUE/FALSE）
    .panduan-option-item, .panduan-option-item-res {
      width: 120px;
      height: 50px;
      font-size: 16px;
    }
  }

  // 提交按钮区域 - 增加左边距
  & .vote-submit-container {
    width: 100px;
    height: 50px;
    margin-left: 16px;
    
    & .vote-submit {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 25px;
      background: rgba(255, 255, 255, 0.3);
      color: rgba(255, 255, 255, 0.7);
      font-weight: bold;
      font-size: 14px;
      transition: all 0.2s ease;
      border: 2px solid transparent;
      
      &.isSelected {
        cursor: pointer;
        background: #fff;
        color: #FF6B00;
        border-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        
        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }
}

// 结果展示样式
.vote-result-inCorrect, .answer-box {
  .option-item, .option-item-res {
    width: 120px !important;
    height: 50px !important;
  }
  
  .option-item-res {
    .option-item-res-desc {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      overflow: hidden;
      
      .option-name, .option-percent {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-weight: bold;
        text-align: center;
      }
      
      .option-name {
        height: 70%;
        background: rgba(255, 255, 255, 0.95) !important;
        color: #333 !important;
        font-weight: bold !important;
      }
      
      .option-percent {
        height: 30%;
        background: rgba(255, 255, 255, 0.8) !important;
        color: #666 !important;
        font-weight: 400 !important;
        font-size: 12px !important;
      }
    }
  }
  
  .panduan-option-item-res {
    .option-item-res-desc {
      .option-name {
        height: 75%;
      }
      .option-percent {
        height: 25%;
      }
    }
  }
  
  .user-selected-name, .correct-answer-option {
    background: rgba(255, 255, 255, 1) !important;
    color: #FF6B00 !important;
    font-weight: bold !important;
  }
  
  .user-selected-percent, .correct-answer-text {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #FF6B00 !important;
    font-weight: 600 !important;
  }
}

// 关闭按钮
.vote-res-close-container {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .icon-close {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    
    &:hover {
      background: #fff;
      transform: scale(1.1);
    }
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      width: 14px;
      height: 2px;
      background: #FF6B00;
      transform: translate(-50%, -50%) rotate(45deg);
    }
    
    &::after {
      transform: translate(-50%, -50%) rotate(-45deg);
    }
  }
}

// 课件选项样式
.option-item-courseware {
  width: 120px !important;
  height: 50px !important;
  
  .option-item-res-desc {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    
    .option-name, .option-text {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      text-align: center;
    }
    
    .option-name {
      height: 70%;
      background: rgba(255, 255, 255, 0.95) !important;
      color: #333 !important;
      font-weight: bold !important;
      font-size: 16px !important;
      
      // 正确答案高亮
      &.correct-answer-option {
        background: rgba(255, 255, 255, 1) !important;
        color: #FF6B00 !important;
        font-weight: bold !important;
      }
    }
    
    .option-text {
      height: 30%;
      background: rgba(255, 255, 255, 0.8) !important;
      color: #666 !important;
      font-weight: 500 !important;
      font-size: 12px !important;
      
      // 正确答案文字高亮
      &.correct-answer-text {
        background: rgba(255, 255, 255, 0.95) !important;
        color: #FF6B00 !important;
        font-weight: 600 !important;
      }
    }
  }
}

// 响应式适配 - 简化版本，专注桌面端体验
@media (max-width: 768px) {
  .vote-bar-container {
    width: 95%;
    max-width: 600px;
    padding: 14px 18px;
    
    & .vote-wrapper, & .vote-wrapper-result {
      gap: 18px;
    }
    
    & .vote-title {
      width: 90px;
      margin-left: 8px;
      font-size: 12px;
      
      p {
        font-size: 12px;
      }
    }
    
    & .timer-box {
      width: 90px;
      margin-left: 8px;
      font-size: 12px;
    }
    
    & .vote-options, & .vote-options-res, & .answer-box {
      gap: 10px;
      margin: 0 12px;
      
      .option-item, .option-item-res {
        width: 55px;
        height: 55px;
        font-size: 16px;
      }
      
      .panduan-option-item, .panduan-option-item-res {
        width: 110px;
        height: 48px;
        font-size: 15px;
      }
    }
    
    & .vote-submit-container {
      width: 90px;
      height: 48px;
      margin-left: 12px;
      
      & .vote-submit {
        font-size: 13px;
      }
    }
  }
}

