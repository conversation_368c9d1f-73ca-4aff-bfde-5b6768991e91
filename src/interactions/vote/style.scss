// 投票选项样式
.vote-bar-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 64.8%;
  height: auto;
  background: url('@/interactions/VoteForCourseware/img/vote-bar-box.png') no-repeat;
  background-size: 100% 100%;
  transition: transform 0.3s ease-in;
  pointer-events: auto;

  & .hide {
    display: none;
  }
  // 伸缩按钮区域
  & .vote-pri-bar-container {
    position: absolute;
    left: 50%;
    background: rgba(0, 0, 0, 0.6);
    transform: translate(-50%, 0px);
    transition: transform 1s;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
  }
  &.ease-in {
    transform: translateY(0);
  }
  &.ease-out {
    transform: translateY(100%);
  }

  & .vote-wrapper, & .vote-wrapper-result {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 6px 6px 0px 0px;
    position: relative;
  }

  & .vote-title, & .timer-box {
    color: #A2AAB8;
    width: 20.5%;
    font-weight: bold;
    line-height: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    p {
      margin-left: 19.3%;
    }
    .countdown {
      color: #FF9F0A;
    }
  }

  & .vote-options, & .vote-options-res, & .answer-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 64%;
    gap: 0 3.12%;
    .option-item, .option-item-res {
      width: 14%;
      height: 0;
      padding-top: 14%;
      line-height: 20px;
      background: #F4F6FA;
      color: #A2AAB8;
      border-radius: 8px;
      text-align: center;
      font-weight: bold;
      cursor: pointer;
      position: relative;
      span {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
      }
      &:hover {
        background: #eceef2;
      }
      &.active {
        background: #FF9F0A;
        color: #fff;
      }
    }
    .panduan-option-item, .panduan-option-item-res, .option-item-courseware {
      width: 48.44%;
      height: 0;
      padding-top: 12.9%;
    }
  }
  & .vote-options-courseware {
    width: 64%;
    & .vote-options {
      width: 100%;
      margin: 0;
    }
  }

  & .vote-submit-container {
      width: 20.5%;
      position: relative;
      height: 50px;
    & .vote-submit {
      position: absolute;
      width: 73.2%;
      height: 100%;
      right: 14.6%;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 22px;
      background: #F4F6FA;
      color: #A2AAB8;
      &.isSelected {
        cursor: pointer;
        background: #ffaa0a;
        color: #fff;
      }
    }
  }
}

// 没有正确答案的投票结果
.vote-result-inCorrect, .answer-box {
  .option-item-res {
    .option-item-res-desc {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      display: flex;
      flex-direction: column;
      .option-name, .option-percent {
        width: 100%;
        color: #a2aab8;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .option-name {
        height: 63.8%;
        background: #F4F6FA;
        border-radius: 8px 8px 0px 0px;
        font-weight: bold;
        line-height: 20px;
      }
      .option-percent {
        height: 36.2%;
        background: #dee2e7;
        border-radius: 0px 0px 8px 8px;
        font-weight: 400;
        line-height: 18px;
      }
    }
  }
  .panduan-option-item-res {
    .option-item-res-desc {
      .option-name {
        height: 69.7%;
        flex-shrink: 0;
      }
      .option-percent {
        height: 30.3%;
      }
    }
  }
  .user-selected-name, .correct-answer-option {
    background: rgba(255,173,11,0.1) !important;
    color: #FFAD0B !important;
  }
  .user-selected-percent, .correct-answer-text {
    background: #FFAD0B !important;
    color: #fff !important;
  }
}
.vote-res-close-container {
  width: 20.5%;
  position: relative;
  display: flex;
  align-items: center;
  .icon-close {
    position: absolute;
    right: 14.6%;
    background: url('./imgs/close.png') no-repeat;
    background-size: cover;
    cursor: pointer;
  }
}

.option-item-courseware {
  .option-item-res-desc {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    .option-name, .option-text {
      width: 100%;
      color: #a2aab8;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .option-name {
      height: 63.8%;
      background: #F4F6FA;
      border-radius: 8px 8px 0px 0px;
      font-weight: bold;
      line-height: 20px;
    }
    .option-text {
      height: 36.2%;
      background: #dee2e7;
      border-radius: 0px 0px 8px 8px;
      font-weight: 400;
      line-height: 18px;
    }
  }
}

