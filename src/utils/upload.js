// @ts-nocheck
import { getSTSToken } from '@/api/overseaBase'
import { blobToFile } from '@/utils/util'
import logger from '@/utils/logger'

// 移除直接导入，改为按需加载
// import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'
// import { Upload } from '@aws-sdk/lib-storage'
// import OSS from 'ali-oss'

export default class Main {
  constructor(options) {
    this.scene = options.scene // 上传场景参数
    this.startUploadTimestamp = 0 // 上传开始时间戳
  }

  /**
   * 上传方法
   * @param {Object} options 上传参数选项
   * @returns Promise
   */
  async putFile(options) {
    const tokenData = await this._getToken({
      scene: this.scene,
      filePaths: [options.filePath],
      fail: options.fail
    })
    if (!tokenData) return
    const { target, filePaths } = tokenData
    this.startUploadTimestamp = this._getTimestamp()
    return new Promise((resolve, reject) => {
      const params = {
        tokenData,
        file: options.file,
        key: filePaths[0],
        progress: options.progress,
        success: options.success,
        fail: options.fail,
        resolve,
        reject
      }
      if (target === 's3') {
        this._s3Upload(params)
      }
      if (target === 'oss') {
        this._ossUpload(params)
      }
    })
  }

  /**
   * 批量上传方法
   * @param {Object} options 上传参数选项
   */
  async putFiles(options) {
    const tokenData = await this._getToken({
      scene: this.scene,
      filePaths: options.filePaths,
      fail: options.fail
    })
    if (!tokenData) return
    const { target, filePaths, appUrl } = tokenData
    return new Promise((_resolve, _reject) => {
      if (!Array.isArray(filePaths)) {
        _reject(new Error(JSON.stringify(tokenData)))
      }
      const uploadQueue = []

      filePaths.forEach((item, index) => {
        uploadQueue.push(
          new Promise((resolve, reject) => {
            const params = {
              tokenData,
              file: options.files[index],
              key: filePaths[index],
              resolve,
              reject
            }
            if (target === 's3') {
              this._s3Upload(params)
            }
            if (target === 'oss') {
              this._ossUpload(params)
            }
          })
        )
      })
      Promise.all(uploadQueue)
        .then(() => {
          const res = {
            filePaths,
            fileUrls: filePaths.map(item => {
              return `${appUrl}/${item}`
            })
          }
          options.success && options.success(res)
          _resolve(res)
        })
        .catch(err => {
          this._sendLogger(
            `批量上传失败, target: ${target}, filePaths: ${JSON.stringify(filePaths)}`,
            'error'
          )
          options.fail && options.fail()
          _reject(err)
        })
    })
  }

  /**
   * 获取上传Token
   * @param {Object} options 参数选项
   * @returns Token Data
   */
  async _getToken(options) {
    const { filePaths } = options
    const params = {
      scene: this.scene,
      filePaths
    }
    const tokenRes = await getSTSToken(params)
    if (!tokenRes || tokenRes.code != 0) {
      this._sendLogger(
        `获取Token失败, params: ${JSON.stringify(params)}, res: ${JSON.stringify(tokenRes)}`
      )
      options.fail && options.fail()
      return false
    }
    return tokenRes.data
  }

  /**
   * AWS S3上传
   * @param {Object} options 上传选项
   */
  async _s3Upload(options) {
    const { file, key, tokenData, progress, resolve, reject } = options
    const bucket = tokenData.bucketName
    const systemClockOffset = tokenData.timestamp * 1000 - new Date().getTime()

    try {
      // 按需导入AWS SDK
      const { S3Client } = await import('@aws-sdk/client-s3')
      const { Upload } = await import('@aws-sdk/lib-storage')

      const s3Client = new S3Client({
        region: tokenData.region || 'us-west-2',
        credentials: {
          accessKeyId: tokenData.accessKey,
          secretAccessKey: tokenData.accessSecret,
          sessionToken: tokenData.sessionToken
        },
        systemClockOffset: systemClockOffset
      })

      const upload = new Upload({
        client: s3Client,
        params: {
          Bucket: bucket,
          Key: key,
          Body: file,
          ContentType: file.type
        },
        leavePartsOnError: false
      })

      upload.on('httpUploadProgress', event => {
        if (progress) {
          const percent = Math.round((event.loaded / event.total) * 100)
          progress(percent)
        }
      })

      const data = await upload.done()
      const url = `${tokenData.appUrl}/${key}`
      this._sendUploadLogger({
        target: 'aws',
        url: url,
        status: 1
      })
      options.success && options.success({ url, key })
      resolve({ url, key })
    } catch (err) {
      this._sendUploadLogger({
        target: 'aws',
        url: key,
        status: 0,
        error: JSON.stringify(err)
      })
      options.fail && options.fail(err)
      reject(err)
    }
  }

  /**
   * Ali OSS上传
   * @param {Object} options 上传选项
   */
  async _ossUpload(options) {
    const { file, key, tokenData, resolve, reject } = options

    try {
      // 按需导入阿里云OSS
      const OSSModule = await import('ali-oss')
      const OSS = OSSModule.default

      const oss = new OSS({
        region: tokenData.region,
        accessKeyId: tokenData.accessKey,
        accessKeySecret: tokenData.accessSecret,
        stsToken: tokenData.sessionToken,
        bucket: tokenData.bucketName,
        timeout: 60000 * 2,
        cname: true,
        endpoint: `${tokenData.bucketName}.${tokenData.endPoint}`
      })

      const _file = blobToFile(file, key.split('/').pop())
      const result = await oss.multipartUpload(key, _file, {
        progress: progress => {
          const percent = Math.round(progress * 100)
          options.progress && options.progress(percent)
        }
      })

      const { res } = result
      if (res && res.statusCode === 200) {
        const url = `${tokenData.appUrl}/${key}`
        this._sendUploadLogger({
          target: 'oss',
          url: url,
          status: 1
        })
        options.success &&
          options.success({
            url,
            key
          })
        resolve({ url, key })
      } else {
        this._sendUploadLogger(
          {
            target: 'oss',
            url: key,
            status: 0,
            error: JSON.stringify(res)
          },
          'error'
        )
        options.fail && options.fail('Upload fail')
        reject('Upload fail')
      }
    } catch (err) {
      this._sendUploadLogger(
        {
          target: 'oss',
          url: key,
          status: 0,
          error: JSON.stringify(err)
        },
        'error'
      )
      options.fail && options.fail(err)
      reject(err)
    }
  }

  /**
   * 获取时间戳
   * @returns 时间戳
   */
  _getTimestamp() {
    return parseInt((new Date().getTime() / 1000).toString())
  }

  /**
   * 日志上报
   * @param {Object} options
   */
  _sendUploadLogger({ target, url, status, error }, level = 'info') {
    logger.send({
      tag: 'student.UploadImage',
      content: {
        msg: status ? '上传成功' : `上传失败, error: ${JSON.stringify(error)}`,
        target,
        uploadPath: url,
        startUploadTimestamp: this.startUploadTimestamp,
        uploadTime: this._getTimestamp() - this.startUploadTimestamp,
        scene: this.scene,
        status
      },
      level
    })
  }

  /**
   * 日志上报
   */
  _sendLogger(msg, level = 'info') {
    logger.send({
      tag: 'uploadFile',
      content: {
        msg: msg
      },
      level
    })
  }
}
// 截图转blob
export function imgBase64ToBlob(base64, mimeType) {
  const bytes = window.atob(base64.split(',')[1])
  const ab = new ArrayBuffer(bytes.length)
  const ia = new Uint8Array(ab)
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i)
  }
  return new Blob([ab], { type: mimeType })
}

/**
 * 上传图片
 */
export async function submitPhoto(file, filePath = 'feedback_screenshot.jpg', scene = 'feedback') {
  const upload = new Main({
    scene
  })
  const { url, key } = await upload.putFile({
    filePath,
    file,
    progress: percent => {
      console.log(percent, 'percent===')
    },
    success: res => {
      console.log(res, '上传截图')
      sendLogger(`上传截图成功, url: ${res.url}`)
    },
    fail: () => {
      sendLogger('上传截图失败')
    }
  })
  console.log(url, 'uploadUrl')
  // 提交数据到后台
  return { url, key }
}
/**
 * base64上传图片,返回图片地址
 * @param {*} base64
 * @param {string} mimeType
 * @param {string} scene
 * @param {string} fileName
 * @returns {string} uploadUrl
 */
export async function base64ToUrl(base64, mimeType, fileName, scene) {
  let blob = imgBase64ToBlob(base64, mimeType)
  const uploadObj = await submitPhoto(blob, fileName, scene)
  return uploadObj
}
/**
 * 上报日志
 */
function sendLogger(msg, params, level = 'info') {
  logger.send({
    tag: 'upload',
    level,
    content: {
      msg,
      params
    }
  })
}
